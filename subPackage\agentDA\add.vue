<template>
  <view class="add-agent-page">
    <!-- 基本信息 -->
    <view class="section">
      <view class="section-header">
        <view class="header-icon">
          <image src="./img/info.svg"></image>
        </view>
        <text class="header-title">基本信息</text>
      </view>

      <view class="form-item">
        <view class="label required">代理商名称</view>
        <input
          v-model="formData.agentName"
          placeholder=""
          class="custom-input"
        />
      </view>

      <view class="form-item">
        <view class="label">代理商编号</view>
        <input
          v-model="formData.agentCode"
          disabled
          class="custom-input disabled"
        />
      </view>

      <view class="form-item">
        <view class="label required">代理商类型</view>
        <view class="radio-group">
          <label class="radio-item" @click="formData.agentType = 'enterprise'">
            <view
              class="radio-icon"
              :class="{ active: formData.agentType === 'enterprise' }"
            >
              <view
                class="radio-dot"
                v-if="formData.agentType === 'enterprise'"
              ></view>
            </view>
            <text>企业</text>
          </label>
          <label class="radio-item" @click="formData.agentType = 'individual'">
            <view
              class="radio-icon"
              :class="{ active: formData.agentType === 'individual' }"
            >
              <view
                class="radio-dot"
                v-if="formData.agentType === 'individual'"
              ></view>
            </view>
            <text>个人</text>
          </label>
        </view>
      </view>

      <view class="form-item">
        <view class="label required">联系电话</view>
        <input
          v-model="formData.phone"
          placeholder=""
          type="number"
          class="custom-input"
        />
      </view>

      <view class="form-item">
        <view class="label required">企业统一信用代码</view>
        <input
          v-model="formData.creditCode"
          placeholder=""
          class="custom-input"
        />
      </view>

      <view style="display: flex; padding: 0 20rpx 20rpx">
        <view style="flex: 1; margin-right: 16rpx">
          <view class="label required">所在省份</view>
          <view class="select-input" @click="showProvincePicker = true">
            <text
              class="select-text"
              :class="{ placeholder: !formData.provinceName }"
            >
              {{ formData.provinceName || "请选择" }}
            </text>
            <van-icon color="#8D9094" name="arrow-down" />
          </view>
        </view>

        <view style="flex: 1">
          <view class="label required">所在城市</view>
          <view class="select-input" @click="showCityPicker = true">
            <text
              class="select-text"
              :class="{ placeholder: !formData.cityName }"
            >
              {{ formData.cityName || "请选择" }}
            </text>
            <van-icon color="#8D9094" name="arrow-down" />
          </view>
        </view>
      </view>

      <view class="form-item">
        <view class="label">详细地址</view>
        <textarea
          v-model="formData.address"
          placeholder=""
          class="custom-textarea"
          auto-height
        />
      </view>

      <view class="form-item">
        <view class="label">主要覆盖区域</view>
        <view class="area-row">
          <view class="select-input" @click="showCoverProvincePicker = true">
            <text
              class="select-text"
              :class="{ placeholder: !formData.coverProvinceName }"
            >
              {{ formData.coverProvinceName || "请选择省份" }}
            </text>
            <van-icon color="#8D9094" name="arrow-down" />
          </view>
          <view class="select-input" @click="showCoverCityPicker = true">
            <text
              class="select-text"
              :class="{ placeholder: !formData.coverCityName }"
            >
              {{ formData.coverCityName || "请选择城市" }}
            </text>
            <van-icon color="#8D9094" name="arrow-down" />
          </view>
        </view>
      </view>

      <view class="form-item">
        <view class="label">团队人员</view>
        <view class="select-input" @click="showTeamPicker = true">
          <text
            class="select-text"
            :class="{ placeholder: !formData.teamMember }"
          >
            {{ formData.teamMember || "请选择" }}
          </text>
          <van-icon color="#8D9094" name="arrow-down" />
        </view>
      </view>
      <view class="form-item">
        <view class="label">财税能力</view>
        <view class="select-input" @click="showTeamPicker = true">
          <text
            class="select-text"
            :class="{ placeholder: !formData.teamMember }"
          >
            {{ formData.teamMember || "请选择" }}
          </text>
          <van-icon color="#8D9094" name="arrow-down" />
        </view>
      </view>
    </view>

    <!-- 意向产品 -->
    <view class="section">
      <view class="section-header">
        <view class="header-icon">
          <image src="./img/product.svg"></image>
        </view>
        <text class="header-title">意向产品</text>
        <view class="add-btn" @click="addProduct">
          <text class="add-icon">+</text>
          <text>添加</text>
        </view>
      </view>

      <view
        v-for="(item, index) in formData.products"
        :key="index"
        class="product-item"
      >
        <view class="product-header">
          <text class="product-name">{{
            item.productName || "血糖仪 X1"
          }}</text>
          <text class="remove-btn" @click="removeProduct(index)">×</text>
        </view>
        <view class="product-info">
          <text
            >代理经验：{{ item.agentExperience === "yes" ? "有" : "无" }}</text
          >
        </view>
        <view class="product-info">
          <text
            >竞品经验：{{
              item.competitorExperience === "yes" ? "有" : "无"
            }}</text
          >
        </view>
        <view class="product-info">
          <text>竞品名称：{{ item.competitorName || "血糖仪X1" }}</text>
        </view>
      </view>
    </view>

    <!-- 意向终端 -->
    <view class="section">
      <view class="section-header">
        <view class="header-icon">
          <image src="./img/term.svg"></image>
        </view>
        <text class="header-title">意向终端</text>
        <view class="add-btn" @click="addTerminal">
          <text class="add-icon">+</text>
          <text>添加</text>
        </view>
      </view>

      <view
        v-for="(item, index) in formData.terminals"
        :key="index"
        class="terminal-item"
      >
        <view class="terminal-header">
          <text class="terminal-name">{{
            item.terminalName || "第一人民医院"
          }}</text>
          <text class="remove-btn" @click="removeTerminal(index)">×</text>
        </view>
        <view class="terminal-info">
          <text>科室：{{ item.department || "内分泌科" }}</text>
        </view>
        <view class="terminal-info">
          <text>代理经验：{{ item.agentExperience || "无" }}</text>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <button class="cancel-button" @click="cancel">取消</button>
      <button class="save-button" @click="save">保存</button>
    </view>

    <!-- 省份选择器 -->
    <view
      class="picker-overlay"
      v-if="showProvincePicker"
      @click="showProvincePicker = false"
    >
      <view class="picker-popup" @click.stop>
        <view class="picker-header">
          <text class="picker-cancel" @click="showProvincePicker = false"
            >取消</text
          >
          <text class="picker-title">选择省份</text>
          <text class="picker-confirm" @click="confirmProvince">确定</text>
        </view>
        <picker-view
          class="picker-view"
          :value="[selectedProvinceIndex]"
          @change="onProvinceChange"
        >
          <picker-view-column>
            <view
              v-for="(item, index) in provinceColumns"
              :key="index"
              class="picker-item"
            >
              {{ item.text }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>

    <!-- 城市选择器 -->
    <view
      class="picker-overlay"
      v-if="showCityPicker"
      @click="showCityPicker = false"
    >
      <view class="picker-popup" @click.stop>
        <view class="picker-header">
          <text class="picker-cancel" @click="showCityPicker = false"
            >取消</text
          >
          <text class="picker-title">选择城市</text>
          <text class="picker-confirm" @click="confirmCity">确定</text>
        </view>
        <picker-view
          class="picker-view"
          :value="[selectedCityIndex]"
          @change="onCityChange"
        >
          <picker-view-column>
            <view
              v-for="(item, index) in cityColumns"
              :key="index"
              class="picker-item"
            >
              {{ item.text }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>

    <!-- 覆盖省份选择器 -->
    <view
      class="picker-overlay"
      v-if="showCoverProvincePicker"
      @click="showCoverProvincePicker = false"
    >
      <view class="picker-popup" @click.stop>
        <view class="picker-header">
          <text class="picker-cancel" @click="showCoverProvincePicker = false"
            >取消</text
          >
          <text class="picker-title">选择覆盖省份</text>
          <text class="picker-confirm" @click="confirmCoverProvince">确定</text>
        </view>
        <picker-view
          class="picker-view"
          :value="[selectedCoverProvinceIndex]"
          @change="onCoverProvinceChange"
        >
          <picker-view-column>
            <view
              v-for="(item, index) in provinceColumns"
              :key="index"
              class="picker-item"
            >
              {{ item.text }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>

    <!-- 覆盖城市选择器 -->
    <view
      class="picker-overlay"
      v-if="showCoverCityPicker"
      @click="showCoverCityPicker = false"
    >
      <view class="picker-popup" @click.stop>
        <view class="picker-header">
          <text class="picker-cancel" @click="showCoverCityPicker = false"
            >取消</text
          >
          <text class="picker-title">选择覆盖城市</text>
          <text class="picker-confirm" @click="confirmCoverCity">确定</text>
        </view>
        <picker-view
          class="picker-view"
          :value="[selectedCoverCityIndex]"
          @change="onCoverCityChange"
        >
          <picker-view-column>
            <view
              v-for="(item, index) in coverCityColumns"
              :key="index"
              class="picker-item"
            >
              {{ item.text }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>

    <!-- 团队人员选择器 -->
    <view
      class="picker-overlay"
      v-if="showTeamPicker"
      @click="showTeamPicker = false"
    >
      <view class="picker-popup" @click.stop>
        <view class="picker-header">
          <text class="picker-cancel" @click="showTeamPicker = false"
            >取消</text
          >
          <text class="picker-title">选择团队人员</text>
          <text class="picker-confirm" @click="confirmTeam">确定</text>
        </view>
        <picker-view
          class="picker-view"
          :value="[selectedTeamIndex]"
          @change="onTeamChange"
        >
          <picker-view-column>
            <view
              v-for="(item, index) in teamColumns"
              :key="index"
              class="picker-item"
            >
              {{ item.text }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
    <!-- 意向产品弹窗 -->
    <view
      class="popup-overlay"
      v-if="showProductPopup"
      @click="showProductPopup = false"
    >
      <view class="popup-content" @click.stop>
        <view class="popup-title">选择意向产品</view>

        <view class="popup-form-item">
          <view class="popup-label required">意向产品</view>
          <view class="select-input" @click="showTeamPicker = true">
            <text
              class="select-text"
              :class="{ placeholder: !formData.teamMember }"
            >
              {{ formData.teamMember || "请选择" }}
            </text>
            <van-icon color="#8D9094" name="arrow-down" />
          </view>
        </view>

        <view class="popup-form-item">
          <view class="popup-label required">代理经验</view>
          <view class="popup-radio-group">
            <label
              class="radio-item"
              @click="productForm.agentExperience = 'yes'"
            >
              <view
                class="radio-icon"
                :class="{ active: productForm.agentExperience === 'yes' }"
              >
                <view
                  class="radio-dot"
                  v-if="productForm.agentExperience === 'yes'"
                ></view>
              </view>
              <text>有</text>
            </label>
            <label
              class="radio-item"
              @click="productForm.agentExperience = 'no'"
            >
              <view
                class="radio-icon"
                :class="{ active: productForm.agentExperience === 'no' }"
              >
                <view
                  class="radio-dot"
                  v-if="productForm.agentExperience === 'no'"
                ></view>
              </view>
              <text>无</text>
            </label>
          </view>
        </view>

        <view class="popup-form-item">
          <view class="popup-label required">竞品经验</view>
          <view class="popup-radio-group">
            <label
              class="radio-item"
              @click="productForm.competitorExperience = 'yes'"
            >
              <view
                class="radio-icon"
                :class="{ active: productForm.competitorExperience === 'yes' }"
              >
                <view
                  class="radio-dot"
                  v-if="productForm.competitorExperience === 'yes'"
                ></view>
              </view>
              <text>有</text>
            </label>
            <label
              class="radio-item"
              @click="productForm.competitorExperience = 'no'"
            >
              <view
                class="radio-icon"
                :class="{ active: productForm.competitorExperience === 'no' }"
              >
                <view
                  class="radio-dot"
                  v-if="productForm.competitorExperience === 'no'"
                ></view>
              </view>
              <text>无</text>
            </label>
          </view>
        </view>

        <view class="popup-form-item">
          <view class="popup-label required">竞品名称</view>
          <input
            v-model="productForm.competitorName"
            placeholder=""
            class="popup-input"
          />
        </view>

        <view class="popup-actions">
          <button class="cancel-btn" @click="cancelProduct">取消</button>
          <button class="confirm-btn" @click="confirmProduct">确定</button>
        </view>
      </view>
    </view>

    <!-- 意向终端弹窗 -->
    <view
      class="popup-overlay"
      v-if="showTerminalPopup"
      @click="showTerminalPopup = false"
    >
      <view class="popup-content" @click.stop>
        <view class="popup-title">选择意向终端</view>

        <view class="popup-form-item">
          <view class="popup-label required">终端名称</view>
          <input
            v-model="terminalForm.terminalName"
            placeholder=""
            class="popup-input"
          />
        </view>

        <view class="popup-form-item">
          <view class="popup-label required">科室</view>
          <input
            v-model="terminalForm.department"
            placeholder=""
            class="popup-input"
          />
        </view>

        <view class="popup-form-item">
          <view class="popup-label required">代理经验</view>
          <view class="popup-radio-group">
            <label
              class="radio-item"
              @click="productForm.competitorExperience = 'yes'"
            >
              <view
                class="radio-icon"
                :class="{ active: productForm.competitorExperience === 'yes' }"
              >
                <view
                  class="radio-dot"
                  v-if="productForm.competitorExperience === 'yes'"
                ></view>
              </view>
              <text>有</text>
            </label>
            <label
              class="radio-item"
              @click="productForm.competitorExperience = 'no'"
            >
              <view
                class="radio-icon"
                :class="{ active: productForm.competitorExperience === 'no' }"
              >
                <view
                  class="radio-dot"
                  v-if="productForm.competitorExperience === 'no'"
                ></view>
              </view>
              <text>无</text>
            </label>
          </view>
        </view>

        <view class="popup-actions">
          <button class="cancel-btn" @click="cancelTerminal">取消</button>
          <button class="confirm-btn" @click="confirmTerminal">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import {
  addAgent,
  editAgent,
  getAgentDetail,
  getProvince,
  getCityByProvinceCode,
} from "@/common/api/agentDA/index.js";

// 页面模式：add-新增，edit-编辑
const mode = ref("add");
const agentId = ref("");

// 表单数据
const formData = reactive({
  agentName: "",
  agentCode: "",
  agentType: "enterprise",
  phone: "",
  creditCode: "",
  provinceCode: "",
  provinceName: "",
  cityCode: "",
  cityName: "",
  address: "",
  coverProvinceCode: "",
  coverProvinceName: "",
  coverCityCode: "",
  coverCityName: "",
  teamMember: "",
  products: [
    {
      productName: "血糖仪 X1",
      agentExperience: "enterprise",
      competitorExperience: "enterprise",
      competitorName: "血糖仪X1",
    },
  ],
  terminals: [
    {
      terminalName: "第一人民医院",
      department: "内分泌科",
      agentExperience: "无",
    },
  ],
});

// 选择器显示状态
const showProvincePicker = ref(false);
const showCityPicker = ref(false);
const showCoverProvincePicker = ref(false);
const showCoverCityPicker = ref(false);
const showTeamPicker = ref(false);
const showProductPopup = ref(false);
const showTerminalPopup = ref(false);

// 选择器数据
const provinceColumns = ref([]);
const cityColumns = ref([]);
const coverCityColumns = ref([]);
const teamColumns = ref([
  { text: "曹玲", value: "曹玲" },
  { text: "张三", value: "张三" },
  { text: "李四", value: "李四" },
]);

// 选择器索引
const selectedProvinceIndex = ref(0);
const selectedCityIndex = ref(0);
const selectedCoverProvinceIndex = ref(0);
const selectedCoverCityIndex = ref(0);
const selectedTeamIndex = ref(0);

// 产品表单
const productForm = reactive({
  productName: "",
  agentExperience: "yes",
  competitorExperience: "yes",
  competitorName: "",
});

// 终端表单
const terminalForm = reactive({
  terminalName: "",
  department: "",
  agentExperience: "",
});

onLoad((options) => {
  if (options.id) {
    agentId.value = options.id;
    mode.value = options.mode || "edit";
    loadAgentDetail();
  }

  initProvinceData();
  generateAgentCode();
});

// 初始化省份数据
const initProvinceData = async () => {
  try {
    const res = await getProvince();
    if (res.code === 0 && res.data) {
      const provinces = res.data;
      provinceColumns.value = provinces.map((item) => ({
        text: item.label,
        value: item.value,
      }));
    }
  } catch (error) {
    console.error("加载省份数据失败:", error);
  }
};

// 生成代理商编号
const generateAgentCode = () => {
  if (mode.value === "add") {
    const timestamp = Date.now().toString().slice(-10);
    formData.agentCode = `SHG${timestamp}`;
  }
};

// 加载代理商详情
const loadAgentDetail = async () => {
  try {
    const res = await getAgentDetail(agentId.value);
    if (res.code === 200) {
      Object.assign(formData, res.data);

      // 更新城市数据
      if (formData.provinceCode) {
        await updateCityColumns(formData.provinceCode);
      }
      if (formData.coverProvinceCode) {
        await updateCoverCityColumns(formData.coverProvinceCode);
      }
    }
  } catch (error) {
    console.error("加载代理商详情失败:", error);
  }
};

// 省份选择变化
const onProvinceChange = (e) => {
  selectedProvinceIndex.value = e.detail.value[0];
};

// 省份选择确认
const confirmProvince = async () => {
  const selectedItem = provinceColumns.value[selectedProvinceIndex.value];
  if (selectedItem) {
    formData.provinceName = selectedItem.text;
    formData.provinceCode = selectedItem.value;

    // 重置城市选择
    formData.cityName = "";
    formData.cityCode = "";
    selectedCityIndex.value = 0;

    // 更新城市数据
    await updateCityColumns(selectedItem.value);
  }
  showProvincePicker.value = false;
};

// 城市选择变化
const onCityChange = (e) => {
  selectedCityIndex.value = e.detail.value[0];
};

// 城市选择确认
const confirmCity = () => {
  const selectedItem = cityColumns.value[selectedCityIndex.value];
  if (selectedItem) {
    formData.cityName = selectedItem.text;
    formData.cityCode = selectedItem.value;
  }
  showCityPicker.value = false;
};

// 覆盖省份选择变化
const onCoverProvinceChange = (e) => {
  selectedCoverProvinceIndex.value = e.detail.value[0];
};

// 覆盖省份选择确认
const confirmCoverProvince = async () => {
  const selectedItem = provinceColumns.value[selectedCoverProvinceIndex.value];
  if (selectedItem) {
    formData.coverProvinceName = selectedItem.text;
    formData.coverProvinceCode = selectedItem.value;

    // 重置覆盖城市选择
    formData.coverCityName = "";
    formData.coverCityCode = "";
    selectedCoverCityIndex.value = 0;

    // 更新覆盖城市数据
    await updateCoverCityColumns(selectedItem.value);
  }
  showCoverProvincePicker.value = false;
};

// 覆盖城市选择变化
const onCoverCityChange = (e) => {
  selectedCoverCityIndex.value = e.detail.value[0];
};

// 覆盖城市选择确认
const confirmCoverCity = () => {
  const selectedItem = coverCityColumns.value[selectedCoverCityIndex.value];
  if (selectedItem) {
    formData.coverCityName = selectedItem.text;
    formData.coverCityCode = selectedItem.value;
  }
  showCoverCityPicker.value = false;
};

// 团队人员选择变化
const onTeamChange = (e) => {
  selectedTeamIndex.value = e.detail.value[0];
};

// 团队人员选择确认
const confirmTeam = () => {
  const selectedItem = teamColumns.value[selectedTeamIndex.value];
  if (selectedItem) {
    formData.teamMember = selectedItem.text;
  }
  showTeamPicker.value = false;
};

// 更新城市列表
const updateCityColumns = async (provinceCode) => {
  if (provinceCode) {
    try {
      const res = await getCityByProvinceCode({ provinceCode });
      if (res.code === 0 && res.data) {
        const cities = res.data;
        cityColumns.value = cities.map((item) => ({
          text: item.label,
          value: item.value,
        }));
      }
    } catch (error) {
      console.error("加载城市数据失败:", error);
      cityColumns.value = [];
    }
  } else {
    cityColumns.value = [];
  }
};

// 更新覆盖城市列表
const updateCoverCityColumns = async (provinceCode) => {
  if (provinceCode) {
    try {
      const res = await getCityByProvinceCode({ provinceCode });
      if (res.code === 0 && res.data) {
        const cities = res.data;
        coverCityColumns.value = cities.map((item) => ({
          text: item.label,
          value: item.value,
        }));
      }
    } catch (error) {
      console.error("加载覆盖城市数据失败:", error);
      coverCityColumns.value = [];
    }
  } else {
    coverCityColumns.value = [];
  }
};

// 添加产品
const addProduct = () => {
  Object.assign(productForm, {
    productName: "",
    agentExperience: "yes",
    competitorExperience: "yes",
    competitorName: "",
  });
  showProductPopup.value = true;
};

// 确认添加产品
const confirmProduct = () => {
  if (!productForm.productName) {
    uni.showToast({
      title: "请输入产品名称",
      icon: "none",
    });
    return;
  }

  formData.products.push({ ...productForm });
  showProductPopup.value = false;
};

// 取消添加产品
const cancelProduct = () => {
  showProductPopup.value = false;
};

// 删除产品
const removeProduct = (index) => {
  formData.products.splice(index, 1);
};

// 添加终端
const addTerminal = () => {
  Object.assign(terminalForm, {
    terminalName: "",
    department: "",
    agentExperience: "",
  });
  showTerminalPopup.value = true;
};

// 确认添加终端
const confirmTerminal = () => {
  if (!terminalForm.terminalName) {
    uni.showToast({
      title: "请输入终端名称",
      icon: "none",
    });
    return;
  }

  formData.terminals.push({ ...terminalForm });
  showTerminalPopup.value = false;
};

// 取消添加终端
const cancelTerminal = () => {
  showTerminalPopup.value = false;
};

// 删除终端
const removeTerminal = (index) => {
  formData.terminals.splice(index, 1);
};

// 表单验证
const validateForm = () => {
  if (!formData.agentName) {
    uni.showToast({
      title: "请输入代理商名称",
      icon: "none",
    });
    return false;
  }

  if (!formData.creditCode) {
    uni.showToast({
      title: "请输入企业统一信用代码",
      icon: "none",
    });
    return false;
  }

  if (!formData.provinceCode) {
    uni.showToast({
      title: "请选择所在省份",
      icon: "none",
    });
    return false;
  }

  if (!formData.cityCode) {
    uni.showToast({
      title: "请选择所在城市",
      icon: "none",
    });
    return false;
  }

  return true;
};

// 保存
const save = async () => {
  if (!validateForm()) return;

  try {
    uni.showLoading({
      title: "保存中...",
    });

    let res;
    if (mode.value === "add") {
      res = await addAgent(formData);
    } else {
      res = await editAgent({ ...formData, id: agentId.value });
    }

    uni.hideLoading();

    if (res.code === 200) {
      uni.showToast({
        title: "保存成功",
        icon: "success",
      });

      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    } else {
      uni.showToast({
        title: res.message || "保存失败",
        icon: "none",
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error("保存失败:", error);
    uni.showToast({
      title: "网络错误",
      icon: "none",
    });
  }
};

// 取消
const cancel = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.add-agent-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  margin: 20rpx 20rpx 0 20rpx;
  padding-bottom: 20rpx;
}

.content {
  flex: 1;
  padding: 16rpx;
}

.section {
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.9);

  /* 下层投影 */
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.12);
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;

  .header-icon {
    width: 36rpx;
    height: 36rpx;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12rpx;

    .icon-text {
      font-size: 18rpx;
      color: #fff;
    }
  }

  .header-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #1a1a1a;
    flex: 1;
  }
}

.add-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  color: #4068f5;
  font-size: 22rpx;
  border-radius: 10rpx;
  border: 1px solid var(--, #4068f5);

  .add-icon {
    font-size: 24rpx;
    margin-right: 6rpx;
  }
}

.form-item {
  padding: 0 24rpx 0;
  margin-bottom: 20rpx;
}

.label {
  font-size: 26rpx;
  color: #1a1a1a;
  margin-bottom: 12rpx;
  font-weight: 500;

  &.required::after {
    content: "*";
    color: #ff4757;
    margin-left: 4rpx;
  }
}

// 原生输入框样式
.custom-input {
  width: 100%;
  height: 64rpx;
  padding: 0 24rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  background: #fff;
  font-size: 24rpx;
  color: #333;
  box-sizing: border-box;

  &:focus {
    border-color: #4285f4;
    outline: none;
  }

  &.disabled {
    background: #f5f5f5;
    color: #999;
  }

  &::placeholder {
    color: #999;
  }
}

// 原生文本域样式
.custom-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx 24rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  background: #fff;
  font-size: 26rpx;
  color: #333;
  box-sizing: border-box;
  resize: none;

  &:focus {
    border-color: #4285f4;
    outline: none;
  }

  &::placeholder {
    color: #999;
  }
}

// 选择输入框样式
.select-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 64rpx;
  padding: 0 24rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  background: #fff;
  box-sizing: border-box;
  cursor: pointer;

  .select-text {
    font-size: 26rpx;
    color: #333;
    flex: 1;

    &.placeholder {
      color: #999;
    }
  }

  .select-arrow {
    font-size: 20rpx;
    color: #999;
    transform: rotate(0deg);
    transition: transform 0.3s ease;
  }

  &:active {
    background: #f8f9fa;
  }
}

// 单选按钮样式
.radio-group {
  display: flex;
  gap: 32rpx;
  margin-top: 8rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;

  .radio-icon {
    width: 24rpx;
    height: 24rpx;
    border: 1rpx solid #d9d9d9;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.active {
      border-color: #4285f4;
      // background: #4285f4;
      .radio-dot {
        background: #4285f4;
      }
    }

    .radio-dot {
      width: 12rpx;
      height: 12rpx;
      background: #fff;
      border-radius: 50%;
    }
  }

  text {
    font-size: 24rpx;
    color: #2f3133;
  }
}

.area-row {
  display: flex;
  gap: 16rpx;
}

.product-item,
.terminal-item {
  padding: 16rpx 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  margin: 12rpx 24rpx;
  background: #fafbfc;

  &:last-child {
    margin-bottom: 24rpx;
  }
}

.product-header,
.terminal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;

  .product-name,
  .terminal-name {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
  }

  .remove-btn {
    color: #8d9094;
    font-size: 28rpx;
  }
}

.product-info,
.terminal-info {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;

  &:last-child {
    margin-bottom: 0;
  }
}

// 选择器弹窗样式
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.picker-popup {
  width: 100%;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fff;

  .picker-cancel,
  .picker-confirm {
    font-size: 28rpx;
    color: #4285f4;
    cursor: pointer;
  }

  .picker-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
  }
}

.picker-view {
  height: 400rpx;
  background: #fff;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

// 底部按钮样式
.bottom-actions {
  display: flex;
  gap: 16rpx;
  padding: 20rpx 24rpx;
  background: #fff;
}

.cancel-button,
.save-button {
  flex: 1;
  height: 72rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-button {
  background: #f5f5f5;
  color: #666;

  &:active {
    background: #e8e8e8;
  }
}

.save-button {
  background: linear-gradient(153deg, #7eadfd 11.75%, #4068f5 75.43%);
  color: #fff;

  &:active {
    background: linear-gradient(153deg, #7eadfd 11.75%, #4068f5 75.43%);
  }
}

// 弹窗样式
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-content {
  width: 85%;
  max-width: 600rpx;
  padding: 32rpx 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

// 弹窗输入框样式
.popup-input {
  width: 100%;
  height: 64rpx;
  padding: 0 24rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  background: #fff;
  font-size: 24rpx;
  color: #333;
  box-sizing: border-box;

  &:focus {
    border-color: #4285f4;
    outline: none;
  }

  &.disabled {
    background: #f5f5f5;
    color: #999;
  }

  &::placeholder {
    color: #999;
  }
}

.popup-title {
  text-align: left;
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-form-item {
  margin-bottom: 24rpx;

  &:last-of-type {
    margin-bottom: 0;
  }
}

.popup-label {
  font-size: 26rpx;
  color: #1a1a1a;
  margin-bottom: 12rpx;
  font-weight: 500;

  &.required::after {
    content: "*";
    color: #ff4757;
    margin-left: 4rpx;
  }
}

.popup-radio-group {
  display: flex;
  gap: 32rpx;
  margin-top: 8rpx;
}

.popup-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 40rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;

  .cancel-btn,
  .confirm-btn {
    flex: 1;
    height: 72rpx;
    border-radius: 8rpx;
    font-size: 26rpx;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    line-height: 72rpx;
  }

  .cancel-btn {
    background: #f5f5f5;
    color: #666;

    &:active {
      background: #e8e8e8;
    }
  }

  .confirm-btn {
    background: linear-gradient(153deg, #7eadfd 11.75%, #4068f5 75.43%);
    color: #fff;

    &:active {
      background: linear-gradient(153deg, #7eadfd 11.75%, #4068f5 75.43%);
    }
  }
}
</style>
